# TurdParty Cleanup Phase Checklists

## Phase 1: Pre-Cleanup Assessment Checklist

### 1.1 Test Coverage Baseline Collection

#### Coverage Report Generation
- [ ] **Run pytest with coverage**
  ```bash
  python -m pytest --cov=api --cov=db --cov=ui --cov=scripts \
    --cov-report=html:test-reports/baseline/html \
    --cov-report=xml:test-reports/baseline/coverage.xml \
    --cov-report=json:test-reports/baseline/coverage.json \
    --cov-report=term > test-reports/baseline/coverage-summary.txt
  ```
  - [ ] Verify HTML report generated
  - [ ] Verify XML report for CI integration
  - [ ] Verify JSON report for programmatic analysis
  - [ ] Save terminal output summary

- [ ] **Run Playwright tests with coverage**
  ```bash
  npx playwright test --reporter=html:test-reports/baseline/playwright-html
  ```
  - [ ] Generate Playwright HTML report
  - [ ] Capture screenshots for UI tests
  - [ ] Document test execution times

- [ ] **Run integration tests**
  ```bash
  ./run-all-tests.sh > test-reports/baseline/integration-results.txt 2>&1
  ```
  - [ ] Execute all integration test scripts
  - [ ] Capture Docker container logs
  - [ ] Document service dependencies

#### Performance Baseline
- [ ] **Measure test execution times**
  - [ ] Unit tests execution time
  - [ ] Integration tests execution time
  - [ ] E2E tests execution time
  - [ ] Total test suite execution time

- [ ] **Document resource usage**
  - [ ] Memory usage during tests
  - [ ] CPU usage patterns
  - [ ] Disk I/O metrics
  - [ ] Network usage for integration tests

#### Test Inventory Creation
- [ ] **Catalog all test files**
  ```bash
  find . -name "test_*.py" -o -name "*test.py" -o -name "*.test.js" -o -name "*.spec.js" > test-reports/baseline/test-files-inventory.txt
  ```
  - [ ] Python test files
  - [ ] JavaScript test files
  - [ ] Configuration files (pytest.ini, playwright.config.js)
  - [ ] Test data files

- [ ] **Map test dependencies**
  - [ ] Database dependencies
  - [ ] External service dependencies
  - [ ] File system dependencies
  - [ ] Network dependencies

### 1.2 File Inventory and Analysis

#### Root Directory Analysis
- [ ] **Generate file inventory**
  ```bash
  ls -la > file-inventory/root-directory-listing.txt
  find . -maxdepth 1 -type f -exec ls -lh {} \; > file-inventory/root-files-detailed.txt
  ```
  - [ ] File sizes and dates
  - [ ] File types and extensions
  - [ ] Last modified timestamps
  - [ ] File permissions

- [ ] **Identify file categories**
  - [ ] Configuration files (.ini, .json, .yml, .yaml)
  - [ ] Test files (test_*, *test.*, *.spec.*, *.test.*)
  - [ ] Documentation files (*.md, *.txt, *.html)
  - [ ] Script files (*.sh, *.py, *.js)
  - [ ] Temporary files (*.log, *.tmp, *.cache)
  - [ ] Build artifacts (*.pyc, node_modules, __pycache__)

#### Dependency Mapping
- [ ] **Analyze import statements**
  ```bash
  grep -r "^import\|^from" --include="*.py" . > file-inventory/python-imports.txt
  grep -r "require\|import" --include="*.js" . > file-inventory/js-imports.txt
  ```
  - [ ] Python module dependencies
  - [ ] JavaScript module dependencies
  - [ ] Relative vs absolute imports
  - [ ] External package dependencies

- [ ] **Identify orphaned files**
  - [ ] Files not referenced by any other file
  - [ ] Files with no recent modifications
  - [ ] Files not covered by tests
  - [ ] Duplicate functionality files

### 1.3 Archive System Setup

#### Create Archive Structure
- [ ] **Initialize archive directory**
  ```bash
  mkdir -p _archive_/{api,frontend,ui,scripts,tests,docs,config,root}
  mkdir -p _archive_/metadata
  ```
  - [ ] Main archive directories
  - [ ] Metadata storage
  - [ ] Cleanup logs directory

- [ ] **Create archive documentation**
  - [ ] Archive README.md with usage instructions
  - [ ] File header template for archived files
  - [ ] Cleanup log template
  - [ ] Restoration procedure documentation

#### Archive Tooling
- [ ] **Create archival scripts**
  - [ ] `scripts/archive-file.sh` - Archive single file with metadata
  - [ ] `scripts/archive-directory.sh` - Archive entire directory
  - [ ] `scripts/restore-from-archive.sh` - Restore archived files
  - [ ] `scripts/validate-archive.sh` - Verify archive integrity

- [ ] **Test archive system**
  - [ ] Archive a test file
  - [ ] Verify metadata is correct
  - [ ] Test restoration process
  - [ ] Validate file integrity

## Phase 2: Core Structure Cleanup Checklist

### 2.1 Root Directory Cleanup

#### Identify Files for Archival
- [ ] **Test files in root**
  - [ ] test-*.js files
  - [ ] test_*.py files
  - [ ] *.test.js files
  - [ ] *.spec.js files
  - [ ] Standalone test configurations

- [ ] **Temporary and debug files**
  - [ ] *.html files (except essential docs)
  - [ ] *.png files (screenshots, temp images)
  - [ ] *.log files
  - [ ] Debug scripts and utilities
  - [ ] Cache files and directories

- [ ] **Duplicate configurations**
  - [ ] Multiple pytest.ini files
  - [ ] Multiple playwright.config.js files
  - [ ] Duplicate Docker configurations
  - [ ] Old environment files

#### Execute Archival Process
- [ ] **Archive test files**
  ```bash
  for file in test-*.js test_*.py *.test.js *.spec.js; do
    if [ -f "$file" ]; then
      ./scripts/archive-file.sh "$file" "Root directory cleanup - moved to /tests"
    fi
  done
  ```
  - [ ] Document original locations
  - [ ] Add archival reason
  - [ ] Verify file integrity
  - [ ] Update cleanup log

- [ ] **Archive temporary files**
  - [ ] Move debug HTML files
  - [ ] Archive screenshot files
  - [ ] Clean up log files
  - [ ] Remove cache directories

#### Validate Root Directory
- [ ] **Verify essential files remain**
  - [ ] README.md
  - [ ] package.json / pyproject.toml
  - [ ] Docker configurations
  - [ ] Main application files
  - [ ] License and documentation

- [ ] **Test basic functionality**
  - [ ] Application starts correctly
  - [ ] Basic API endpoints work
  - [ ] Docker containers build
  - [ ] Essential scripts execute

### 2.2 Test Consolidation

#### Consolidate Test Files
- [ ] **Move tests to /tests directory**
  - [ ] Create subdirectories: unit/, integration/, e2e/
  - [ ] Move Python tests to appropriate subdirectories
  - [ ] Move JavaScript tests to appropriate subdirectories
  - [ ] Update import paths in moved files

- [ ] **Merge duplicate test configurations**
  - [ ] Consolidate pytest.ini files
  - [ ] Merge playwright.config.js files
  - [ ] Update test discovery paths
  - [ ] Standardize test markers and categories

#### Update Test References
- [ ] **Update CI/CD configurations**
  - [ ] GitHub Actions workflows
  - [ ] Docker test configurations
  - [ ] Test runner scripts
  - [ ] Coverage report paths

- [ ] **Update documentation**
  - [ ] Test execution instructions
  - [ ] Development setup guides
  - [ ] CI/CD documentation
  - [ ] Troubleshooting guides

#### Validate Test Consolidation
- [ ] **Run consolidated test suite**
  ```bash
  python -m pytest tests/ -v
  npx playwright test
  ```
  - [ ] All tests discovered correctly
  - [ ] No import errors
  - [ ] Coverage reports generate
  - [ ] Test execution times acceptable

## Phase 3: Advanced Organization Checklist

### 3.1 Directory-Specific Cleanup

#### API Directory Organization
- [ ] **Review /api structure**
  - [ ] Organize routes by functionality
  - [ ] Group related models
  - [ ] Consolidate service files
  - [ ] Clean up test files

- [ ] **Archive unused API files**
  - [ ] Old route implementations
  - [ ] Deprecated models
  - [ ] Unused middleware
  - [ ] Debug endpoints

#### Scripts Directory Organization
- [ ] **Categorize scripts by function**
  - [ ] testing/ - Test-related scripts
  - [ ] deployment/ - Deployment and CI scripts
  - [ ] development/ - Development utilities
  - [ ] maintenance/ - Cleanup and maintenance

- [ ] **Archive duplicate scripts**
  - [ ] Multiple test runners
  - [ ] Duplicate deployment scripts
  - [ ] Old utility scripts
  - [ ] Debug scripts

#### Frontend Directory Cleanup
- [ ] **Clean build artifacts**
  - [ ] node_modules (if committed)
  - [ ] Build output directories
  - [ ] Cache files
  - [ ] Temporary files

- [ ] **Organize source files**
  - [ ] Component organization
  - [ ] Asset management
  - [ ] Configuration files
  - [ ] Test file organization

### 3.2 Configuration Standardization

#### Docker Configuration Cleanup
- [ ] **Consolidate Docker files**
  - [ ] Remove duplicate Dockerfiles
  - [ ] Merge similar configurations
  - [ ] Standardize naming conventions
  - [ ] Update documentation

- [ ] **Clean up Docker Compose files**
  - [ ] Remove unused services
  - [ ] Standardize service names
  - [ ] Consolidate environment variables
  - [ ] Update volume mappings

#### Environment Configuration
- [ ] **Standardize environment files**
  - [ ] Consolidate .env templates
  - [ ] Remove duplicate configurations
  - [ ] Document required variables
  - [ ] Update setup instructions

## Phase 4: Validation and Documentation Checklist

### 4.1 Post-Cleanup Testing

#### Comprehensive Test Execution
- [ ] **Run complete test suite**
  ```bash
  # Unit tests
  python -m pytest tests/unit/ --cov=api --cov=db --cov=ui
  
  # Integration tests
  python -m pytest tests/integration/
  
  # E2E tests
  npx playwright test
  
  # Full test suite
  ./run-all-tests.sh
  ```

#### Coverage Comparison
- [ ] **Generate post-cleanup coverage**
  - [ ] Same format as baseline
  - [ ] Same test scope
  - [ ] Same reporting options
  - [ ] Same execution environment

- [ ] **Compare coverage metrics**
  ```bash
  ./scripts/compare-coverage.sh test-reports/baseline/ test-reports/post-cleanup/
  ```
  - [ ] Line coverage comparison
  - [ ] Branch coverage comparison
  - [ ] Function coverage comparison
  - [ ] File coverage comparison

#### Performance Validation
- [ ] **Compare execution times**
  - [ ] Test suite execution time
  - [ ] Individual test performance
  - [ ] CI/CD pipeline time
  - [ ] Application startup time

### 4.2 Documentation Updates

#### Update Project Documentation
- [ ] **Main README.md**
  - [ ] Updated folder structure
  - [ ] New setup instructions
  - [ ] Updated test execution
  - [ ] Archive system documentation

- [ ] **Directory-specific READMEs**
  - [ ] /tests/README.md
  - [ ] /scripts/README.md
  - [ ] /api/README.md
  - [ ] /_archive_/README.md

#### Create Maintenance Guidelines
- [ ] **Cleanup procedures**
  - [ ] Regular maintenance tasks
  - [ ] File organization guidelines
  - [ ] Archive system usage
  - [ ] Code review checklist

- [ ] **Development guidelines**
  - [ ] New file placement rules
  - [ ] Naming conventions
  - [ ] Test organization
  - [ ] Documentation requirements

## Success Validation Checklist

### Final Verification
- [ ] **All tests pass**
- [ ] **Coverage maintained (≥95% of baseline)**
- [ ] **No functionality regression**
- [ ] **Documentation updated**
- [ ] **Archive system functional**
- [ ] **CI/CD pipeline works**
- [ ] **Development environment setup works**
- [ ] **Performance metrics acceptable**

### Cleanup Log Completion
- [ ] **Document all archived files**
- [ ] **Record all moved files**
- [ ] **Note all configuration changes**
- [ ] **Update dependency documentation**
- [ ] **Create restoration procedures**
